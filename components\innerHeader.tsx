import { Box, Button, H1, HR, Text } from '@bigcommerce/big-design';
import { ArrowBackIcon } from '@bigcommerce/big-design-icons';
import { useRouter } from 'next/router';
import { TabIds, TabRoutes } from './header';

const InnerHeader = () => {
    const router = useRouter();
    const { feeId } = router.query;

    // Navigate to Order Fee tab as the main navigation
    const handleBackClick = () => router.push(TabRoutes[TabIds.ORDER_FEE]);

    // Determine the page title based on the current route
    let pageTitle = '';
    if (router.pathname.includes('/create-fee')) {
        pageTitle = 'Create Fee Rule';
    } else if (router.pathname.includes('/edit-fee') && feeId) {
        pageTitle = 'Edit Fee Rule';
    }

    return (
        <Box marginBottom="xxLarge">
            <Button iconLeft={<ArrowBackIcon color="secondary50" />} variant="subtle" onClick={handleBackClick}>
                <Text bold color="secondary50">Back to Order Fee</Text>
            </Button>
            {pageTitle &&
                <H1>{pageTitle}</H1>
            }
            <HR color="secondary30" />
        </Box>
    );
};

export default InnerHeader;
