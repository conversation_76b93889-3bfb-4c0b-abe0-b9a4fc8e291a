import { useRouter } from 'next/router';
import Index from '@pages/index';
import { render, screen } from '@test/utils';

jest.mock('next/router', () => ({
    useRouter: jest.fn(),
}));

describe('Homepage Redirect', () => {
    test('renders loading component while redirecting', () => {
        const mockReplace = jest.fn();
        const router = { replace: mockReplace };
        (useRouter as jest.Mock).mockReturnValue(router);

        const { container } = render(<Index />);
        const loadingText = screen.getByText('Loading...');

        expect(container.firstChild).toMatchSnapshot();
        expect(loadingText).toBeInTheDocument();
        expect(mockReplace).toHaveBeenCalledWith('/order-fee');
    });
});
