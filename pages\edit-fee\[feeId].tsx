import {
    Box,
    Button,
    Checkbox,
    Flex,
    Form,
    FormGroup,
    H1,
    Input,
    Panel,
    Select,
    Text,
} from '@bigcommerce/big-design';
import { useRouter } from 'next/router';
import { FormEvent, useEffect, useState } from 'react';
import ErrorMessage from '../../components/error';
import Loading from '../../components/loading';
import CheckboxList from '../../components/CheckboxList';
import ProductFilter from '../../components/ProductFilter';
import { useSession } from '../../context/session';
import { useFeeRule, useBrands, useCategories, useCustomerGroups, useChannels, useTaxClasses, useNameValidation } from '../../lib/hooks';

interface FeeFormData {
    name: string;
    type: 'percentage' | 'fixed';
    display_name: string;
    cost: number;
    source: string;
    tax_class_id: number | '';
    selectedBrands: number[];
    selectedCategories: number[];
    selectedCustomerGroups: number[];
    selectedChannels: number[];
    selectedProducts: number[];
    active: boolean;
}

interface FormErrors {
    [key: string]: string;
}

const EditFee = () => {
    const router = useRouter();
    const { feeId } = router.query;
    const { context } = useSession();
    const { feeRule, isLoading: feeRuleLoading, error: feeRuleError } = useFeeRule(feeId as string);
    const { brands, isLoading: brandsLoading, error: brandsError } = useBrands();
    const { categories, isLoading: categoriesLoading, error: categoriesError } = useCategories();
    const { customerGroups, isLoading: customerGroupsLoading, error: customerGroupsError } = useCustomerGroups();
    const { channels, isLoading: channelsLoading, error: channelsError } = useChannels();
    const { taxClasses, isLoading: taxClassesLoading, error: taxClassesError } = useTaxClasses();
    const { validateName } = useNameValidation();

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string>('');
    const [formData, setFormData] = useState<FeeFormData>({
        name: '',
        type: 'percentage',
        display_name: '',
        cost: 0,
        source: '',
        tax_class_id: '',
        selectedBrands: [],
        selectedCategories: [],
        selectedCustomerGroups: [],
        selectedChannels: [],
        selectedProducts: [],
        active: true,
    });
    const [errors, setErrors] = useState<FormErrors>({});

    // Log channels when they're loaded
    useEffect(() => {
        if (channels.length > 0) {
            console.log("Channels in edit-fee:", channels);
        }
    }, [channels]);

    // Populate form when fee rule data is loaded
    useEffect(() => {
        if (feeRule) {
            setFormData({
                name: feeRule.name || '',
                type: feeRule.type || 'percentage',
                display_name: feeRule.display_name || '',
                cost: feeRule.cost || 0,
                source: 'APP', // Always use 'APP' regardless of what's in the database
                tax_class_id: feeRule.tax_class_id || '',
                selectedBrands: feeRule.selectedBrands || [],
                selectedCategories: feeRule.selectedCategories || [],
                selectedCustomerGroups: feeRule.selectedCustomerGroups || [],
                selectedChannels: feeRule.selectedChannels || [],
                selectedProducts: feeRule.selectedProducts || [],
                active: feeRule.active !== undefined ? feeRule.active : true,
            });
        }
    }, [feeRule]);

    const validateForm = async (): Promise<boolean> => {
        const newErrors: FormErrors = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        } else {
            // Validate name uniqueness during form submission (exclude current fee rule)
            const isNameValid = await validateName(formData.name, feeId as string);
            if (!isNameValid) {
                newErrors.name = 'Fee rule name must be unique. Please choose a different name.';
            }
        }

        if (!formData.display_name.trim()) {
            newErrors.display_name = 'Display name is required';
        }

        // Remove source validation

        if (formData.cost === undefined || formData.cost < 0) {
            newErrors.cost = 'Cost must be a non-negative number';
        }

        if (formData.tax_class_id !== '' && (formData.tax_class_id < 0 || !Number.isInteger(formData.tax_class_id))) {
            newErrors.tax_class_id = 'Tax class ID must be a non-negative integer';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleInputChange = (field: keyof FeeFormData, value: any) => {
        setFormData(prev => ({ ...prev, [field]: value }));

        // Clear error for this field when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    const handleSubmit = async (event: FormEvent) => {
        event.preventDefault();

        setIsSubmitting(true);
        setSubmitError('');

        // Validate form including name uniqueness
        const isFormValid = await validateForm();
        if (!isFormValid) {
            setIsSubmitting(false);
            return;
        }

        try {
            const submitData: any = {
                feeRuleId: feeId,
                name: formData.name.trim(),
                type: formData.type,
                display_name: formData.display_name.trim(),
                cost: formData.cost,
                source: 'APP', // Always use 'APP' as the source
                active: formData.active,
                selectedBrands: formData.selectedBrands,
                selectedCategories: formData.selectedCategories,
                selectedCustomerGroups: formData.selectedCustomerGroups,
                selectedChannels: formData.selectedChannels,
                selectedProducts: formData.selectedProducts,
            };

            // Only include optional fields if they have values
            if (formData.tax_class_id !== '' && typeof formData.tax_class_id === 'number') {
                submitData.tax_class_id = formData.tax_class_id;
            }

            const response = await fetch(`/api/fee-rules?context=${context}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(submitData),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to update fee rule');
            }

            // Show success message with synchronization results
            let successMessage = 'Fee rule updated successfully!';

            if (result.synchronization) {
                if (result.synchronization.attempted) {
                    if (result.synchronization.success) {
                        successMessage += ` BigCommerce fee synchronized in checkout ${result.synchronization.checkoutId}.`;
                    } else {
                        successMessage += ` Note: BigCommerce synchronization failed (${result.synchronization.error}).`;
                    }
                } else {
                    successMessage += ` ${result.synchronization.reason}.`;
                }
            }

            // Log success message and any synchronization details
            console.log(successMessage);
            if (result.synchronization?.details) {
                console.log('Synchronization details:', result.synchronization.details);
            }

            // Redirect back to order-fee page on success
            router.push('/order-fee');
        } catch (error) {
            console.error('Error updating fee rule:', error);
            setSubmitError(error.message || 'An error occurred while updating the fee rule');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        router.push('/order-fee');
    };

    if (!context || feeRuleLoading) {
        return <Loading />;
    }

    if (feeRuleError) {
        return <ErrorMessage error={feeRuleError} />;
    }

    if (!feeRule) {
        return (
            <Box>
                <H1>Fee Rule Not Found</H1>
                <Text>The requested fee rule could not be found.</Text>
                <Button marginTop="medium" onClick={handleCancel}>
                    Back to Fee Management
                </Button>
            </Box>
        );
    }

    return (
        <Box>
            <Flex justifyContent="flex-end" marginBottom="large">
                <Button variant="subtle" onClick={handleCancel}>
                    Cancel
                </Button>
            </Flex>

            {submitError && (
                <Box marginBottom="medium">
                    <ErrorMessage error={new Error(submitError)} />
                </Box>
            )}

            <Panel>
                <Form onSubmit={handleSubmit}>
                    <FormGroup>
                        <Input
                            label="Name"
                            name="name"
                            placeholder="Enter fee rule name"
                            required
                            value={formData.name}
                            error={errors.name}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                        />
                    </FormGroup>

                    <FormGroup>
                        <Select
                            label="Type"
                            name="type"
                            required
                            value={formData.type}
                            onOptionChange={(value) => handleInputChange('type', value as 'percentage' | 'fixed')}
                            options={[
                                { value: 'percentage', content: 'Percentage' },
                                { value: 'fixed', content: 'Fixed Amount' },
                            ]}
                        />
                    </FormGroup>

                    <FormGroup>
                        <Input
                            label="Display Name"
                            name="display_name"
                            placeholder="Enter display name for customers"
                            required
                            value={formData.display_name}
                            error={errors.display_name}
                            onChange={(e) => handleInputChange('display_name', e.target.value)}
                        />
                    </FormGroup>

                    <FormGroup>
                        <Input
                            label={`Cost ${formData.type === 'percentage' ? '(%)' : '($)'}`}
                            name="cost"
                            type="number"
                            min="0"
                            step={formData.type === 'percentage' ? '0.01' : '0.01'}
                            placeholder={formData.type === 'percentage' ? 'Enter percentage (e.g., 5.5)' : 'Enter fixed amount (e.g., 10.00)'}
                            required
                            value={formData.cost}
                            error={errors.cost}
                            onChange={(e) => handleInputChange('cost', parseFloat(e.target.value) || 0)}
                        />
                    </FormGroup>

                    {/* <FormGroup>
                        <Input
                            label="Source"
                            name="source"
                            placeholder="Enter source (e.g., APP, STORE)"
                            required
                            value={formData.source}
                            error={errors.source}
                            onChange={(e) => handleInputChange('source', e.target.value)}
                        />
                        <Text color="secondary60" marginTop="xSmall">
                            Required by BigCommerce API. Use "APP" for application-generated fees.
                        </Text>
                    </FormGroup> */}

                    <FormGroup>
                        <Select
                            label="Tax Class"
                            name="tax_class_id"
                            value={formData.tax_class_id === '' ? '' : formData.tax_class_id.toString()}
                            onOptionChange={(value) => handleInputChange(
                                'tax_class_id', 
                                value === '' ? '' : parseInt(value)
                            )}
                            options={[
                                { value: '', content: 'Choose tax class (optional)' },
                                ...(taxClasses || []).map(taxClass => ({
                                    value: taxClass.id.toString(),
                                    content: taxClass.name
                                }))
                            ]}
                            error={errors.tax_class_id}
                        />
                        {taxClassesLoading && (
                            <Text color="secondary60" marginTop="xSmall">
                                Loading tax classes...
                            </Text>
                        )}
                    </FormGroup>

                    <CheckboxList
                        label="Categories"
                        items={categories.map(cat => ({ id: cat.category_id, name: cat.name }))}
                        selectedIds={formData.selectedCategories}
                        onChange={(selectedIds) => handleInputChange('selectedCategories', selectedIds)}
                        loading={categoriesLoading}
                        error={categoriesError}
                    />

                    <CheckboxList
                        label="Brands"
                        items={brands.map(brand => ({ id: brand.id, name: brand.name }))}
                        selectedIds={formData.selectedBrands}
                        onChange={(selectedIds) => handleInputChange('selectedBrands', selectedIds)}
                        loading={brandsLoading}
                        error={brandsError}
                    />

                    <CheckboxList
                        label="Customer Groups"
                        items={customerGroups.map(group => ({ id: group.id, name: group.name }))}
                        selectedIds={formData.selectedCustomerGroups}
                        onChange={(selectedIds) => handleInputChange('selectedCustomerGroups', selectedIds)}
                        loading={customerGroupsLoading}
                        error={customerGroupsError}
                    />

                    <CheckboxList
                        label="Channels"
                        items={channels.map(channel => ({ id: channel.id, name: channel.name }))}
                        selectedIds={formData.selectedChannels}
                        onChange={(selectedIds) => handleInputChange('selectedChannels', selectedIds)}
                        loading={channelsLoading}
                        error={channelsError}
                    />

                    <ProductFilter
                        selectedIds={formData.selectedProducts}
                        onChange={(selectedIds) => handleInputChange('selectedProducts', selectedIds)}
                    />

                    <FormGroup>
                        <Checkbox
                            label="Active"
                            checked={formData.active}
                            onChange={(e) => handleInputChange('active', e.target.checked)}
                        />
                        <Text color="secondary60" marginTop="xSmall">
                            Check to make this fee rule active and applicable to orders
                        </Text>
                    </FormGroup>

                    <Flex justifyContent="flex-end" marginTop="large">
                        <Button
                            variant="subtle"
                            marginRight="medium"
                            onClick={handleCancel}
                            disabled={isSubmitting}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? 'Updating...' : 'Update Fee Rule'}
                        </Button>
                    </Flex>
                </Form>
            </Panel>
        </Box>
    );
};

export default EditFee;
